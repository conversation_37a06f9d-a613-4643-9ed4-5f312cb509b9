rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Users collection with optimized pagination
    match /users/{userId} {
      // Allow read/write for own user document
      allow read, write: if isAuthenticated()
        && request.auth.uid == userId;

      // Allow admin to read all users (remove pagination limit restriction)
      allow read: if isAdmin();

      // Allow admin to write user data
      allow write: if isAdmin();

      // Allow reading users collection for authenticated users
      allow list: if isAuthenticated();
    }
    
    // Documents collection with enhanced role-based access and unlimited queries
    match /document-metadata/{documentId} {
      // Allow single document read for authenticated users
      allow get: if isAuthenticated();

      // ENHANCED: Allow unlimited queries for admin users
      allow list: if isAdmin();

      // Allow collection queries for regular users with reasonable limits
      allow list: if isAuthenticated()
        && !isAdmin()
        && request.query.limit <= 100;

      // Allow write for document owner or admin with unified ID validation
      allow write: if isAuthenticated()
        && (request.auth.uid == resource.data.uploadedBy || isAdmin())
        && (
          // If updating, ensure ID consistency is maintained
          !('id' in request.resource.data) ||
          request.resource.data.id == documentId
        );

      // Allow create for authenticated users with enhanced validation for unified ID system
      allow create: if isAuthenticated()
        && request.auth.uid == request.resource.data.uploadedBy
        && request.resource.data.keys().hasAll(['id', 'fileName', 'uploadedAt', 'isActive'])
        && request.resource.data.id == documentId  // Ensure unified ID consistency
        && request.resource.data.isActive == true
        && request.resource.data.status in ['active', 'pending']
        && request.resource.data.fileName is string
        && request.resource.data.fileName.size() > 0
        && request.resource.data.fileName.size() <= 255;

      // ENHANCED: Unlimited batch operations for admin users
      allow list: if isAdmin()
        && 'isActive' in request.query.where
        && request.query.where.isActive == true;

      // Regular users batch operations with limits
      allow list: if isAuthenticated()
        && !isAdmin()
        && request.query.limit <= 50
        && 'isActive' in request.query.where
        && request.query.where.isActive == true;

      // ADMIN-ONLY DELETE POLICY: Only administrators can delete documents
      // This enforces the admin-only file deletion policy preference
      allow delete: if isAdmin();
    }
    
    // Categories collection with unlimited access
    match /categories/{categoryId} {
      // Allow single document read for authenticated users
      allow get: if isAuthenticated();

      // ENHANCED: Allow unlimited queries for categories (small dataset)
      allow list: if isAuthenticated();

      // Only admin can write categories
      allow write: if isAdmin();
    }

    // Activities collection with time-based cleanup support
    match /activities/{activityId} {
      // Allow single document read for authenticated users
      allow get: if isAuthenticated();

      // Allow collection queries for recent activities only with pagination
      allow list: if isAuthenticated()
        && request.query.limit <= 20
        && request.query.orderBy == 'timestamp';

      // Allow create for authenticated users
      allow create: if isAuthenticated();

      // Allow admin to delete old activities (for cleanup)
      allow delete: if isAdmin();
    }

    // Upload statistics collection for real-time file count tracking
    match /upload-statistics/{statId} {
      // Allow read for authenticated users
      allow read: if isAuthenticated();

      // Allow write for authenticated users (for real-time updates)
      allow write: if isAuthenticated();

      // Allow admin full access
      allow read, write: if isAdmin();
    }

    // File validation logs collection (for security monitoring)
    match /file-validation-logs/{logId} {
      // Allow read for admin only
      allow read: if isAdmin();

      // Allow create for authenticated users (for logging validation results)
      allow create: if isAuthenticated();

      // Allow admin to delete old logs
      allow delete: if isAdmin();
    }

    // Duplicate file detection cache
    match /duplicate-cache/{cacheId} {
      // Allow read/write for authenticated users (for duplicate detection)
      allow read, write: if isAuthenticated();

      // Allow admin full access
      allow read, write, delete: if isAdmin();
    }

    // Database version tracking collection
    match /database-version/{versionId} {
      // Allow read for authenticated users (for version checking)
      allow read: if isAuthenticated();

      // Allow write for admin only (for version updates)
      allow write: if isAdmin();
    }
  }
}
